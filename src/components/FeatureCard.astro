---
interface Props {
	icon: string;
	title: string;
	description: string;
}

const { icon, title, description } = Astro.props;
---

<div class="feature-card">
	<span class="feature-icon">{icon}</span>
	<h3>{title}</h3>
	<p>{description}</p>
</div>

<style>
	.feature-card {
		background: rgba(255, 255, 255, 0.05);
		padding: 2rem;
		border-radius: 16px;
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.1);
		transition: transform 0.3s, box-shadow 0.3s;
	}

	.feature-card:hover {
		transform: translateY(-5px);
		box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
	}

	.feature-icon {
		font-size: 2.5rem;
		margin-bottom: 1rem;
		display: block;
	}

	h3 {
		margin: 0 0 0.5rem;
		color: #fff;
		font-size: 1.5rem;
	}

	p {
		margin: 0;
		color: rgba(255, 255, 255, 0.8);
		line-height: 1.6;
	}
</style> 