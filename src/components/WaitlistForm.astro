<form class="waitlist-form" id="waitlistForm">
	<div class="input-group">
		<input type="email" id="email" placeholder="Enter your email" required>
		<button type="submit" class="submit-btn">Join the Waitlist</button>
	</div>
</form>

<style>
	.waitlist-form {
		max-width: 500px;
		margin: 0 auto;
	}

	.input-group {
		display: flex;
		gap: 1rem;
		background: rgba(255, 255, 255, 0.1);
		padding: 0.5rem;
		border-radius: 12px;
		backdrop-filter: blur(10px);
		border: 1px solid rgba(255, 255, 255, 0.2);
	}

	input {
		flex: 1;
		background: transparent;
		border: none;
		padding: 1rem;
		color: #fff;
		font-size: 1rem;
		font-family: inherit;
	}

	input::placeholder {
		color: rgba(255, 255, 255, 0.5);
	}

	input:focus {
		outline: none;
	}

	.submit-btn {
		background: linear-gradient(45deg, #4a90e2, #7c4dff);
		color: white;
		border: none;
		padding: 1rem 2rem;
		border-radius: 8px;
		font-size: 1rem;
		font-weight: 500;
		cursor: pointer;
		transition: transform 0.2s, box-shadow 0.2s;
	}

	.submit-btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(124, 77, 255, 0.3);
	}

	.submit-btn:active {
		transform: translateY(0);
	}

	@media (max-width: 640px) {
		.input-group {
			flex-direction: column;
		}

		.submit-btn {
			width: 100%;
		}
	}
</style>

<script>
	const form = document.getElementById('waitlistForm');
	
	form?.addEventListener('submit', async (e) => {
		e.preventDefault();
		
		const emailInput = form.querySelector('input[type="email"]') as HTMLInputElement;
		const email = emailInput.value;
		
		try {
			// Here you would typically send the email to your backend
			// For now, we'll just show a success message
			alert('Thank you for joining our waitlist! We\'ll be in touch soon.');
			emailInput.value = '';
		} catch (error) {
			alert('Something went wrong. Please try again later.');
		}
	});
</script> 