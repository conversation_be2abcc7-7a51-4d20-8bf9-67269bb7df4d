import { useState, useEffect } from 'react';

// Predefined tagline pairs
const TAGLINE_PAIRS = [
  {
    yin: "Sell like starlight—silent, steady, seen everywhere.",
    yang: "Hack gravity; watch revenue fall upwards."
  },
  {
    yin: "Cold-fusion operations. Hot-fusion imagination.",
    yang: "We automate the mundane so you can author the mythic."
  },
  {
    yin: "Paperclips to wormholes—one platform.",
    yang: "Your inbox finds customers before they know they exist."
  },
  {
    yin: "Quantum marketing, classical results.",
    yang: "Where hot logic meets cool mysticism."
  },
  {
    yin: "Turning your stardust into starlight-level sales.",
    yang: "Cosmic vision, earthly revenue."
  },
  {
    yin: "First-mover momentum meets Apple-grade word-play.",
    yang: "CIA-tight execution with galactic imagination."
  }
];

export default function ParadoxTaglines() {
  const [taglinePair, setTaglinePair] = useState(TAGLINE_PAIRS[0]);
  
  useEffect(() => {
    // Select a random tagline pair on component mount
    const randomIndex = Math.floor(Math.random() * TAGLINE_PAIRS.length);
    setTaglinePair(TAGLINE_PAIRS[randomIndex]);
  }, []);

  return (
    <div className="paradox-taglines">
      <div className="tagline-container">
        <div className="tagline yin">
          <span className="label">YIN</span>
          <p>{taglinePair.yin}</p>
        </div>
        <div className="tagline yang">
          <span className="label">YANG</span>
          <p>{taglinePair.yang}</p>
        </div>
      </div>
      <p className="note">Each visit spawns a new paradoxical pair, courtesy of our on-page agent.</p>
      
      <style jsx>{`
        .paradox-taglines {
          margin: 4rem 0;
          text-align: center;
        }
        
        .tagline-container {
          display: flex;
          gap: 2rem;
          margin-bottom: 1rem;
        }
        
        .tagline {
          flex: 1;
          padding: 2rem;
          border-radius: 12px;
          position: relative;
          backdrop-filter: blur(10px);
          transition: transform 0.3s, box-shadow 0.3s;
        }

        .tagline:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        .yin {
          background: rgba(0, 0, 0, 0.4);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .yang {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .label {
          position: absolute;
          top: -10px;
          left: 20px;
          background: #000;
          padding: 0 10px;
          font-size: 0.8rem;
          color: rgba(168, 178, 255, 0.8);
          font-weight: 600;
          letter-spacing: 1px;
        }

        .tagline p {
          margin: 0;
          font-size: 1.1rem;
          line-height: 1.4;
          color: rgba(255, 255, 255, 0.9);
          font-style: italic;
        }
        
        .note {
          font-size: 0.9rem;
          color: rgba(255, 255, 255, 0.5);
          font-style: italic;
          margin-top: 1rem;
        }
        
        @media (max-width: 768px) {
          .tagline-container {
            flex-direction: column;
            gap: 1.5rem;
          }

          .tagline {
            padding: 1.5rem;
          }

          .tagline p {
            font-size: 1rem;
          }
        }
      `}</style>
    </div>
  );
}
