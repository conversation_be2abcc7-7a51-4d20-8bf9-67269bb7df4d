---
// Component Imports
import Counter from '../components/Counter';
import Layout from '../layouts/Layout.astro';
import WaitlistForm from '../components/WaitlistForm.astro';
import FeatureCard from '../components/FeatureCard.astro';
const someProps = {
	count: 0,
};

// Full Astro Component Syntax:
// https://docs.astro.build/basics/astro-components/
---

<Layout title="Reality Engineering | Where Stories Transcend Dimensions">
	<main class="container">
		<div class="stars-container">
			<img src="/stars.png" alt="Starry background" class="stars-image" />
			<div class="fade-overlay"></div>
		</div>
		<div class="content">
			<h1 class="title">Reality Engineering</h1>
			<p class="tagline">Where Stories Transcend Dimensions</p>
			
			<div class="description">
				<p>At the intersection of cosmic wonder and human imagination, we craft narratives that bridge the gap between the infinite and the intimate.</p>
				<p>Join us on a journey where technology meets nature, where stars whisper secrets, and where every story is a portal to new dimensions.</p>
			</div>

			<WaitlistForm />

			<div class="features">
				<FeatureCard
					icon="🌌"
					title="Cosmic Narratives"
					description="Stories that span galaxies and touch souls"
				/>
				<FeatureCard
					icon="🌍"
					title="Ancient Wisdom"
					description="Ancient knowledge meets modern insight"
				/>
				<FeatureCard
					icon="⚡"
					title="Digital Alchemy"
					description="Technology that transforms storytelling"
				/>
			</div>
		</div>
	</main>
</Layout>

<style>
	.container {
		min-height: 100vh;
		position: relative;
		z-index: 5;
	}

	.stars-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 1;
		height: auto;
		aspect-ratio: 1792/1024;
	}

	.stars-image {
		width: 100%;
		height: auto;
		display: block;
		max-width: 100%;
	}

	.fade-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 1) 100%);
		pointer-events: none;
	}

	.content {
		position: relative;
		z-index: 10;
		max-width: 1200px;
		margin: 0 auto;
		padding: 4rem 2rem;
		text-align: center;
	}

	.title {
		font-size: 6rem;
		font-weight: 700;
		margin-bottom: 1rem;
		background: linear-gradient(45deg, #fff, #a8b2ff);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.tagline {
		font-size: 2rem;
		color: #a8b2ff;
		margin-bottom: 2rem;
	}

	.description {
		max-width: 800px;
		margin: 0 auto 3rem;
		color: #e0e0ff;
		line-height: 1.6;
	}

	.description p {
		margin-bottom: 1rem;
	}

	.features {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 2rem;
		margin-top: 4rem;
	}

	@media (max-width: 768px) {
		.title {
			font-size: 2.5rem;
		}
		
		.tagline {
			font-size: 1.2rem;
		}
		
		.content {
			padding: 2rem 1rem;
		}
	}
</style>
