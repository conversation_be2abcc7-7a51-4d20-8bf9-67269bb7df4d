---
import Layout from '../layouts/Layout.astro';
import WaitlistForm from '../components/WaitlistForm.astro';
import FeatureCard from '../components/FeatureCard.astro';
import ParadoxTaglines from '../components/ParadoxTaglines.jsx';
import ServicePortal from '../components/ServicePortal.astro';
import FaqAccordion from '../components/FaqAccordion.jsx';
---

<Layout title="Reality Engineering | Where Cosmic Vision Meets Earthly Revenue">
	<main class="container">
		<div class="stars-container">
			<img src="/stars.png" alt="Starry background" class="stars-image" />
			<div class="fade-overlay"></div>
		</div>
		<div class="content">
			<h1 class="title">Engineer Reality.™</h1>
			<p class="tagline">A paradox-powered growth engine that is both everywhere and unseen—as familiar as your phone buzz and as mysterious as dark energy.</p>

			<div class="description">
				<p>From omni-channel AI automations that text, talk, and teleport your brand into customers' hands to immersive storyworlds that magnetize loyalty, REA turns cosmic vision into earthly revenue.</p>
			</div>

			<WaitlistForm />

			<ParadoxTaglines client:load />

			<section class="why-us">
				<h2>Why Creators Choose Our Constellation</h2>
				<div class="features">
					<FeatureCard
						icon="✨"
						title="From Stardust to Starlight"
						description="Your raw creative atoms compressed into sales-supernova funnels through AI sequences that 38% of SMBs already swear by."
					/>
					<FeatureCard
						icon="🛡️"
						title="First Line of Delight, Last Line of Defense"
						description="We deploy ops as quietly as Langley but guard your brand integrity with CIA-grade rigor."
					/>
					<FeatureCard
						icon="📊"
						title="Proof, Not Promises"
						description="Generative messaging lifts click-throughs 3-4× for early adopters; we wire those gains straight into your bottom line."
					/>
				</div>
			</section>

			<section class="services">
				<h2>Three Portals, One Pushbutton</h2>
				<div class="service-portals">
					<ServicePortal
						number="01"
						title="AI Automations & Omni-Channel Messaging"
						description="RCS, SMS, email, socials—one intelligence speaks fluent customer. 24/7 outreach with quantum precision."
					/>
					<ServicePortal
						number="02"
						title="Engaging Storytelling & Immersive Experiences"
						description="Interactive storyworlds drop shoppers into a branded multiverse; immersion becomes revenue."
					/>
					<ServicePortal
						number="03"
						title="Covert Innovation Lab"
						description="Custom agents, volumetric media, and sacred-geometry UX too classified to list—available to academy graduates."
					/>
				</div>
			</section>

			<section class="podcast">
				<h2>Reality Engineering Podcast // Loading...</h2>
				<p>Tune in Q3 2025 for transmissions that fuse Carl Sagan's "we are star-stuff" wonder with concrete conversion hacks.</p>
			</section>

			<section class="faq">
				<h2>FAQ – Paradox Edition</h2>
				<FaqAccordion client:visible />
			</section>

			<section class="final-cta">
				<h2>Reality won't bend itself.</h2>
				<p>Dial in. Load out. Let's make stardust profitable.</p>
				<WaitlistForm />
			</section>
		</div>
	</main>
</Layout>

<style>
	.container {
		min-height: 100vh;
		position: relative;
		z-index: 5;
	}

	.stars-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 1;
		height: auto;
		aspect-ratio: 1792/1024;
	}

	.stars-image {
		width: 100%;
		height: auto;
		display: block;
		max-width: 100%;
	}

	.fade-overlay {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 1) 100%);
		pointer-events: none;
	}

	.content {
		position: relative;
		z-index: 10;
		max-width: 1200px;
		margin: 0 auto;
		padding: 4rem 2rem;
		text-align: center;
	}

	.title {
		font-size: 6rem;
		font-weight: 700;
		margin-bottom: 1rem;
		background: linear-gradient(45deg, #fff, #a8b2ff);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.tagline {
		font-size: 1.5rem;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 2rem;
		max-width: 900px;
		margin-left: auto;
		margin-right: auto;
		line-height: 1.4;
	}

	.description {
		max-width: 800px;
		margin: 0 auto 3rem;
		color: #e0e0ff;
		line-height: 1.6;
		font-size: 1.1rem;
	}

	.description p {
		margin-bottom: 1rem;
	}

	h2 {
		font-size: 2.5rem;
		margin: 4rem 0 2rem;
		text-align: center;
		background: linear-gradient(45deg, #fff, #a8b2ff);
		-webkit-background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	section {
		margin: 6rem 0;
	}

	.features {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 2rem;
		margin-top: 2rem;
	}

	.service-portals {
		display: flex;
		flex-direction: column;
		gap: 1.5rem;
		margin-top: 2rem;
	}

	.podcast, .final-cta {
		text-align: center;
		padding: 3rem;
		border-radius: 16px;
		background: rgba(255, 255, 255, 0.03);
		border: 1px solid rgba(255, 255, 255, 0.08);
	}

	.podcast h2, .final-cta h2 {
		margin-top: 0;
	}

	.faq {
		margin: 4rem 0;
	}

	@media (max-width: 768px) {
		.title {
			font-size: 3rem;
		}

		.tagline {
			font-size: 1.2rem;
		}

		.content {
			padding: 2rem 1rem;
		}

		h2 {
			font-size: 2rem;
		}

		.features {
			grid-template-columns: 1fr;
		}
	}
</style>
